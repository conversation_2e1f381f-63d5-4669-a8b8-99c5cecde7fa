<template>
  <el-form ref="formRef" :model="formData" :rules="formRules" v-loading="formLoading" label-width="0px" :inline-message="true">
    <el-table :data="formData" class="-mt-10px">
      <el-table-column label="序号" type="index" width="100"/>
      <el-table-column label="项目名称" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item
            :prop="`${$index}.projectName`"
            :rules="formRules.projectName"
            class="mb-0px!"
          >
            <el-input v-model="row.projectName" placeholder="请输入项目名称"/>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="公司名称" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item
            :prop="`${$index}.companyName`"
            :rules="formRules.companyName"
            class="mb-0px!"
          >
            <el-input v-model="row.companyName" placeholder="请输入公司名称"/>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="开始时间" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.startDate`" :rules="formRules.startDate" class="mb-0px!">
            <el-date-picker
              v-model="row.startDate"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="选择开始时间"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.endDate`" :rules="formRules.endDate" class="mb-0px!">
            <el-date-picker
              v-model="row.endDate"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="选择结束时间"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="技术栈" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item
            :prop="`${$index}.technologies`"
            :rules="formRules.technologies"
            class="mb-0px!"
          >
            <el-input v-model="row.technologies" placeholder="请输入技术栈"/>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="项目描述" min-width="200">
        <template #default="{ row, $index }">
          <el-form-item
            :prop="`${$index}.projectDesc`"
            :rules="formRules.projectDesc"
            class="mb-0px!"
          >
            <el-input v-model="row.projectDesc" type="textarea" placeholder="请输入项目描述"/>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="项目职位" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item
            :prop="`${$index}.projectRole`"
            :rules="formRules.projectRole"
            class="mb-0px!"
          >
            <el-input v-model="row.projectRole" placeholder="请输入项目职位"/>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="项目职责" min-width="200">
        <template #default="{ row, $index }">
          <el-form-item
            :prop="`${$index}.responsibilities`"
            :rules="formRules.responsibilities"
            class="mb-0px!"
          >
            <el-input v-model="row.responsibilities" type="textarea" placeholder="请输入项目职责"/>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="项目业绩" min-width="200">
        <template #default="{ row, $index }">
          <el-form-item
            :prop="`${$index}.projectAchievement`"
            :rules="formRules.projectAchievement"
            class="mb-0px!"
          >
            <el-input
              v-model="row.projectAchievement"
              type="textarea"
              placeholder="请输入项目业绩"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="60">
        <template #default="{ $index }">
          <el-button @click="handleDelete($index)" link>—</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <el-row justify="center" class="mt-3">
    <el-button @click="handleAdd" round>+ 添加项目经历</el-button>
  </el-row>
</template>
<script setup lang="ts">
import {PersonApi} from '@/api/hr/person'

const props = defineProps<{
  personId: number // 人员ID（主表的关联字段）
}>()
const formLoading = ref(false) // 表单的加载中
const formData = ref<any[]>([])
const formRules = reactive({
  personId: [{required: true, message: '人员ID不能为空', trigger: 'blur'}],
  startDate: [{required: true, message: '开始时间不能为空', trigger: 'blur'}],
  endDate: [{required: true, message: '结束时间不能为空', trigger: 'blur'}]
})
const formRef = ref() // 表单 Ref

/** 监听主表的关联字段的变化，加载对应的子表数据 */
watch(
  () => props.personId,
  async (val) => {
    // 1. 重置表单
    formData.value = []
    // 2. val 非空，则加载数据
    if (!val) {
      return
    }
    try {
      formLoading.value = true
      formData.value = await PersonApi.getPersonProjectListByPersonId(val)
    } finally {
      formLoading.value = false
    }
  },
  {immediate: true}
)

/** 新增按钮操作 */
const handleAdd = () => {
  const row = {
    id: undefined,
    personId: undefined,
    projectName: undefined,
    companyName: undefined,
    startDate: undefined,
    endDate: undefined,
    technologies: undefined,
    projectDesc: undefined,
    projectRole: undefined,
    responsibilities: undefined,
    projectAchievement: undefined
  }
  row.personId = props.personId as any
  formData.value.push(row)
}

/** 删除按钮操作 */
const handleDelete = (index) => {
  formData.value.splice(index, 1)
}

/** 表单校验 */
const validate = () => {
  return formRef.value.validate()
}

/** 表单值 */
const getData = () => {
  return formData.value
}

defineExpose({validate, getData})
</script>
